import React, { useContext, useEffect, useRef, useState } from 'react';
import { Upload, Heading, FormLabel, Flex, Text, IconButton, ToastContext, Box } from '@majoo-ui/react';
import { CheckFilled, CloseFilled, DownloadOutline, ReportOutline } from '@majoo-ui/icons';
import { colors } from '~/stitches.config';
import { catchError, downloadFileHelper, formatBytes, getFileNameFromUrl, getFileSizeFromUrl } from '~/utils/helper';
import { UPLOAD_FILE_ADDRESS } from '~/pages/CashBook/TransferList/components/FormModal';
import { TOKEN_UPLOAD } from '~/pages/Promotion/BasePromoV2/retina/DetailDialog/utils';
import { clearSession } from '~/services/api/session.util';
import PropTypes from 'prop-types';
import LoadingSpinner from '~/components/helper/loadingSpinner';
import ImagePreviewModal from '~/components/retina/modal/ImagePreviewModal';
import { useMediaQuery } from '~/utils/useMediaQuery';
import { ContentWrapper, LabelWrapper, RowBox } from './Form.styles';

const FileStatus = {
    UPLOADING: 'uploading',
    DONE: 'done',
    SAVED: 'saved',
};

const FileCard = ({ file, onRemove }) => {
    const modalTriggerRef = useRef(null);

    const handleFilePreview = () => {
        if (file.status !== FileStatus.SAVED) return;
        const extension = String(file.name.split('.').pop()).toLowerCase();
        if (extension === 'pdf') {
            window.open(file.url, '_blank');
        } else if (extension === 'jpg' || extension === 'png' || extension === 'jpeg') {
            modalTriggerRef.current.click();
        } else {
            downloadFileHelper(file.url);
        }
    };

    return (
        <Flex
            align="center"
            gap={3}
            css={{
                padding: '$spacing-04 $spacing-05',
                borderRadius: '$lg',
                border: '1px solid $bgBorder',
                width: '100%',
                overflow: 'hidden',
            }}
        >
            <Box css={{ flexShrink: 0 }}>
                <ReportOutline />
            </Box>
            <Flex
                direction="column"
                css={{
                    flex: '1 1 auto',
                    minWidth: 0,
                    overflow: 'hidden',
                }}
            >
                <Text
                    variant="label"
                    color={file.status === FileStatus.SAVED ? 'green' : 'primary'}
                    isTruncated
                    onClick={handleFilePreview}
                    css={{
                        cursor: file.status === FileStatus.SAVED ? 'pointer' : 'default',
                        width: '100%',
                    }}
                >
                    {file.name}
                </Text>
                {file.size ? (
                    <Text variant="helper" color="secondary" isTruncated>
                        {formatBytes(file.size)}
                    </Text>
                ) : null}
            </Flex>
            <Flex align="center" gap={3} css={{ flexShrink: 0 }}>
                {file.status === FileStatus.DONE && <CheckFilled color={colors.hlSuccess} />}
                {file.status === FileStatus.UPLOADING && <LoadingSpinner size={20} borderWidth={2} />}
                {file.status === FileStatus.SAVED && (
                    <IconButton type="button" css={{ padding: 0 }} onClick={() => downloadFileHelper(file.url)}>
                        <DownloadOutline color={colors.iconDisable} />
                    </IconButton>
                )}
                <IconButton type="button" onClick={() => onRemove(file)} css={{ padding: 0 }}>
                    <CloseFilled color={colors.iconDisable} />
                </IconButton>
            </Flex>
            <ImagePreviewModal ref={modalTriggerRef} previewImage={file.url} />
        </Flex>
    );
};

FileCard.propTypes = {
    file: PropTypes.shape({
        name: PropTypes.string,
        size: PropTypes.number,
        status: PropTypes.string,
        url: PropTypes.string,
    }),
    onRemove: PropTypes.func,
};

FileCard.defaultProps = {
    file: null,
    onRemove: null,
};

const UploadEvidenceSection = ({ t, onChange, initialUrls }) => {
    const { addToast } = useContext(ToastContext);
    const isMobile = useMediaQuery('(max-width: 766px)');
    const [files, setFiles] = useState([]);
    const [fileError, setFileError] = useState('');
    const [isUploading, setIsUploading] = useState(false);

    const handleFileChange = ({ file }) => {
        if (file && (!file.status || file.status === FileStatus.UPLOADING)) {
            if (
                file.type &&
                (file.type.includes('xlsx') ||
                    file.type.includes('spreadsheet') ||
                    file.type.includes('pdf') ||
                    file.type.includes('jpg') ||
                    file.type.includes('png') ||
                    file.type.includes('xls') ||
                    file.type.includes('zip') ||
                    file.type.includes('doc') ||
                    file.type.includes('docx') ||
                    file.type.includes('jpeg'))
            ) {
                const fileSize = (file.size / 1024 / 1024).toFixed(1);
                if (fileSize > 2) {
                    setFileError('Tidak dapat mengunggah file. Pastikan ukuran file tidak lebih dari 2 MB.');
                    return;
                }
                setFiles([
                    {
                        ...file,
                        status: FileStatus.UPLOADING,
                    },
                    ...files,
                ]);
                setFileError('');
            } else {
                setFileError('Format file tidak didukung. Silakan unggah file dengan tipe yang sesuai.');
            }
        }
    };

    const handlePhotoRemove = removedFile => {
        setFiles(current => current.filter(file => file.url !== removedFile.url));
        setFileError('');
    };

    useEffect(() => {
        onChange(files.filter(file => file.status !== FileStatus.UPLOADING).map(file => file.url));
    }, [files]);

    useEffect(async () => {
        if (initialUrls) {
            await Promise.all(
                initialUrls.map(async url => {
                    const size = await getFileSizeFromUrl(url);
                    return {
                        name: getFileNameFromUrl(url),
                        size,
                        status: FileStatus.SAVED,
                        url,
                    };
                }),
            ).then(val => {
                setFiles(val);
            });
        }
    }, []);

    return (
        <RowBox>
            <LabelWrapper css={{ marginTop: 0 }}>
                <FormLabel className="form-label">
                    <Heading as="h3" heading="sectionSubTitle">
                        Pilih File
                    </Heading>
                </FormLabel>
            </LabelWrapper>
            <ContentWrapper
                css={{
                    display: 'flex',
                    gap: '$spacing-05',
                    flexDirection: 'column',
                }}
            >
                <Flex align={isMobile ? 'start' : 'center'} gap={3} direction={isMobile ? 'column' : 'row'}>
                    <Box
                        onClick={() => {
                            if (files.length >= 5)
                                setFileError(
                                    'Tidak dapat menambah file. Jumlah file yang diunggah tidak boleh lebih dari 5',
                                );
                        }}
                        css={{ width: '100%' }}
                    >
                        <Upload
                            showCropper={false}
                            accept=".png,.jpg,.jpeg,.pdf,.doc,.docx,.xls,.xlsx,.zip"
                            onChange={handleFileChange}
                            action={UPLOAD_FILE_ADDRESS}
                            token={TOKEN_UPLOAD}
                            maxSizeMB={2}
                            customBodyRequest={file => ({ userfile: file })}
                            resKey="item_image_path"
                            placeholder="Pilih atau letakkan berkas di sini"
                            onProgress={() => setIsUploading(true)}
                            onSuccess={val => {
                                if (val.item_image_path) {
                                    setFiles(current =>
                                        current.map(file => {
                                            if (file.status === FileStatus.UPLOADING) {
                                                return {
                                                    ...file,
                                                    status: FileStatus.DONE,
                                                    url: val.item_image_path,
                                                };
                                            }
                                            return file;
                                        }),
                                    );
                                }
                                setIsUploading(false);
                            }}
                            onError={(error, response) => {
                                setFiles(current => current.filter(file => file.status !== FileStatus.UPLOADING));
                                if (error) {
                                    addToast({
                                        title: t('toast.somethingWrong', 'Terjadi Kesalahan', { ns: 'translation' }),
                                        description: catchError(response),
                                        variant: 'failed',
                                        position: 'top-right',
                                        dismissAfter: 3000,
                                    });
                                    if (error.message === 'Unauthorized') {
                                        setTimeout(() => {
                                            clearSession();
                                        }, 1000);
                                    }
                                }
                                setIsUploading(false);
                            }}
                            disabled={isUploading || files.length >= 5}
                            width={isMobile ? '100%' : 320}
                            css={{
                                width: isMobile ? '100%' : 'auto',
                            }}
                        />
                    </Box>
                    <Flex direction="column">
                        <Text variant="helper" color="secondary">
                            1. File dapat berupa Excel, Word, PDF, JPG, PNG, atau ZIP
                        </Text>
                        <Text variant="helper" color="secondary">
                            2. Maksimal 5 File
                        </Text>
                        <Text variant="helper" color="secondary">
                            3. Maksimal 2 MB per file
                        </Text>
                    </Flex>
                </Flex>
                {fileError && (
                    <Text variant="helper" color="red">
                        {fileError}
                    </Text>
                )}
                {files.map(file => (
                    <FileCard
                        key={`${file.name || file.url}_${file.status}`}
                        file={file}
                        onRemove={handlePhotoRemove}
                    />
                ))}
            </ContentWrapper>
        </RowBox>
    );
};

UploadEvidenceSection.propTypes = {
    t: PropTypes.func.isRequired,
    onChange: PropTypes.func.isRequired,
    initialUrls: PropTypes.arrayOf(PropTypes.string),
};

UploadEvidenceSection.defaultProps = {
    initialUrls: [],
};

export default UploadEvidenceSection;
