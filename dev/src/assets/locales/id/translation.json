{"general": {"feature": "fitur", "feature_titleCase": "<PERSON><PERSON>"}, "save": "Simpan", "legend": {"sales": "Penjualan", "transaction": "Transaksi", "grossProfit": "<PERSON><PERSON>", "product": "Produk", "profit": "Lab<PERSON>", "employee": "<PERSON><PERSON><PERSON>"}, "period": {"hour": "Jam", "hours": "Jam", "day": "<PERSON>", "week": "<PERSON><PERSON>", "month": "<PERSON><PERSON><PERSON>", "months": "<PERSON><PERSON><PERSON>", "monthly": "<PERSON><PERSON><PERSON>", "year": "<PERSON><PERSON>", "jam": "Jam", "hari": "<PERSON>", "minggu": "<PERSON><PERSON>", "bulan": "<PERSON><PERSON><PERSON>", "tahun": "<PERSON><PERSON>", "menit": "Menit", "second": "<PERSON><PERSON>", "seconds": "<PERSON><PERSON>", "daily": "<PERSON><PERSON>", "weekly": "Mingguan", "monthly2": "Bulanan", "yearly": "<PERSON><PERSON><PERSON>"}, "label": {"weight": "<PERSON><PERSON>", "attention": "<PERSON><PERSON><PERSON><PERSON>", "group": "Grup", "saveAsDraft": "Simpan Dr<PERSON>", "comparison": "<PERSON><PERSON><PERSON><PERSON>", "export": "Ekspor", "exportReport": "Ekspor Laporan", "chooseCsvDelimiter": "<PERSON><PERSON><PERSON> (Delimiter)", "downloadReport": "<PERSON><PERSON><PERSON>", "downloadProduct": "Unduh Daftar Produk", "exportTemplate": "Ekspor Template", "importData": "Impor Data", "exportData": "Ekspor Data", "cancel": "<PERSON><PERSON>", "cancelled": "Batalkan", "continue": "Ya, Lanjutkan", "continue2": "Lanjutkan", "complete": "<PERSON><PERSON><PERSON><PERSON>", "apply": "Terapkan", "confirm": "<PERSON><PERSON>, <PERSON>ger<PERSON>", "waiting": "Tunggu...", "deleting": "<PERSON><PERSON><PERSON><PERSON>", "processing": "Memproses", "importConfirmation": "Konfirmasi Impor", "deleteConfirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "showDetail": "<PERSON><PERSON>", "nextOption": "Opsi Lanjutan", "advanceOptions": "Opsi Lanjutan", "delete": "Hapus", "add": "Tambahkan", "added": "ditambah", "saved": "disimpan", "edited": "<PERSON><PERSON><PERSON>", "changed": "<PERSON><PERSON><PERSON>", "changes": "<PERSON><PERSON><PERSON>", "deleted": "di<PERSON><PERSON>", "exporting": "Mengekspor", "addProduct": "Tambah Produk", "addCategory": "Tambah Kategori", "product": "Produk", "setProduct": "Tetapkan Produk", "products": "<PERSON><PERSON><PERSON> barang", "process": "Proses", "noData": "Tidak ada data", "edit": "Ubah", "editProduct": "Ubah Produk", "add2": "Tambah", "detail": "Detail", "phoneNo": "No. Telepon", "address": "<PERSON><PERSON><PERSON>", "country": "Negara", "province": "<PERSON><PERSON><PERSON>", "city": "Kota", "accountNumber": "Nomor <PERSON>", "description": "Keterangan", "settings": "<PERSON><PERSON><PERSON><PERSON>", "period": "Periode", "category": "<PERSON><PERSON><PERSON>", "stock": "Stok", "price": "<PERSON><PERSON>", "save": "Simpan", "productName": "<PERSON><PERSON>", "categoryName": "<PERSON><PERSON>", "chooseCategory": "<PERSON><PERSON><PERSON>", "active": "Aktif", "inactive": "Tidak Aktif", "deleteProduct": "Hapus Produk", "importProductData": "Impor Data Produk", "choose": "<PERSON><PERSON><PERSON>", "basicPrice": "<PERSON><PERSON>", "averagePrice": "<PERSON><PERSON>", "purchasePrice": "<PERSON><PERSON>", "sellingPrice": "<PERSON><PERSON>", "normalPrice": "Harga <PERSON>", "materialName": "<PERSON><PERSON>", "unitName": "<PERSON><PERSON>", "back": "Kembali", "department": "Departemen", "next": "<PERSON><PERSON><PERSON><PERSON>", "nextThen": "Selanjutnya", "done": "Se<PERSON><PERSON>", "yes": "Ya", "no": "Tidak", "information": "Informasi", "max": "Ma<PERSON>.", "learnMore": "<PERSON><PERSON><PERSON><PERSON>", "hide": "Sembunyikan", "show": "Selengkapnya", "unhide": "<PERSON><PERSON><PERSON><PERSON>", "showing": "menampilkan", "loading": "Memuat...", "submit": "Submit", "request": "Aju<PERSON>", "claim": "<PERSON><PERSON><PERSON>", "allOutlets": "<PERSON><PERSON><PERSON>", "imageUpload": "Unggah Gambar", "imageChange": "Ubah Gambar", "buyNow": "<PERSON><PERSON>", "dateRange": "<PERSON><PERSON><PERSON>", "selectDate": "<PERSON><PERSON><PERSON>", "showAll": "<PERSON><PERSON><PERSON><PERSON>", "showLess": "<PERSON><PERSON><PERSON><PERSON>", "outletCounter": "Outlet terpilih", "showBy": "<PERSON><PERSON><PERSON>", "inRupiah": "<PERSON><PERSON>", "trxType": "<PERSON><PERSON>", "person": "Orang", "persons": "Orang", "reportByDate": "La<PERSON>an by <PERSON><PERSON>", "reportByTime": "<PERSON><PERSON><PERSON> by <PERSON><PERSON><PERSON>", "statusPayment": "Status Bayar", "orderType": "Jenis Order", "priceType": "<PERSON><PERSON>", "adding": "menambahkan", "approve": "<PERSON><PERSON><PERSON>", "integrationProcess": "Proses Integrasi", "callUs": "<PERSON><PERSON><PERSON><PERSON>", "upgradeNow": "Upgrade Sekarang", "sync": "Sink<PERSON><PERSON><PERSON>", "syncOrder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chooseTime": "<PERSON><PERSON><PERSON>", "startTime": "<PERSON><PERSON>", "endTime": "<PERSON><PERSON><PERSON>", "sku": "SKU", "unit": "Satuan", "processIntegration": "Proses Integrasi", "productCategory": "<PERSON><PERSON><PERSON>", "otherMenu": "<PERSON><PERSON><PERSON>", "chatPanelButton": "Chat 24 Jam", "draft": "Draf", "emptyDataDesc": "<paragraph>belum ada data yang dapat di<PERSON>pilkan<br/> di halaman ini</paragraph>", "mainPhoto": "Foto utama", "showcase": "Etalase", "noDataYet": "Belum ada data", "tryAgain": "<PERSON><PERSON>", "selected": "<PERSON><PERSON><PERSON><PERSON>", "resubmit": "<PERSON><PERSON><PERSON>", "sort": "Urut<PERSON>", "share": "Bagikan", "logActivity": "Log Aktivitas", "lastUpdated": "<PERSON><PERSON><PERSON> {{date}}", "lastUpdatedBy": "<PERSON><PERSON><PERSON> {{date}} oleh {{by}}", "lastUpdatedBy_empty": "<PERSON><PERSON><PERSON> -", "import": "Impor", "itemSelected": "item dipilih", "sequence": "Urutan", "package": "<PERSON><PERSON>", "withoutParentGroup": "Tanpa Induk Group", "noCategory": "Tidak Ada Kategori", "print": "Cetak", "copy": "<PERSON><PERSON>", "copied": "Te<PERSON>lin!", "or": "<PERSON><PERSON>", "use": "<PERSON><PERSON><PERSON>", "wait": "Menunggu...", "ok": "<PERSON>e", "skip": "<PERSON><PERSON>", "send": "<PERSON><PERSON>", "selectedData": "data terpilih", "set": "Atur", "mainOutlet": "Outlet Utama", "lastPurchasePrice": "<PERSON><PERSON>", "maximum": "<PERSON><PERSON><PERSON><PERSON>", "setView": "Atur Tampilan", "filterBy": "<PERSON><PERSON>", "compareWith": "<PERSON><PERSON><PERSON> den<PERSON>", "perDate": "<PERSON>", "start": "<PERSON><PERSON>", "end": "<PERSON><PERSON><PERSON>", "confirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "importAddData": "Impor Tambah Data", "deactivate": "Non Aktif", "activate": "Aktifkan", "duplicate": "Duplikat", "createdBy": "Dibuat Oleh", "createdDate": "Tanggal Dibuat", "selectAll": "<PERSON><PERSON><PERSON>", "maxRangeDate": "Maksimal range adalah {{maxRange}} hari"}, "placeholder": {"search": "Cari ...", "searchWithoutDots": "<PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>", "example": "Contoh: ", "category": "<PERSON><PERSON><PERSON>", "allCategory": "<PERSON><PERSON><PERSON>", "merchant": "<PERSON><PERSON><PERSON>", "findCategory": "<PERSON><PERSON>", "status": "Semua Status", "allDepartment": "<PERSON><PERSON><PERSON>", "input": "Masukka<PERSON>", "noImage": "Tidak ada gambar", "type": "<PERSON><PERSON>", "inputHere": "Input di sini", "selectDate": "<PERSON><PERSON><PERSON> tanggal", "selectTime": "<PERSON><PERSON><PERSON> waktu"}, "status": {"active": "Aktif", "inactive": "Tidak Aktif", "category": "<PERSON><PERSON><PERSON>", "allCategory": "<PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON>", "showInMenu": "Tampil di Menu", "hideInMenu": "Tidak Tampil di Menu", "allowed": "<PERSON><PERSON><PERSON><PERSON>", "notAllowed": "Tidak diizinkan", "high": "Tingg<PERSON>", "medium": "Sedang", "low": "Rendah", "integrated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notIntegrated": "Non Integrasi"}, "select": {"categoryDefaultSelectOption": "<PERSON><PERSON><PERSON>", "allOrderType": "<PERSON><PERSON><PERSON>", "typeSelectTitle": "Pilih Type", "select": "<PERSON><PERSON><PERSON>", "typeSelectOption": {"default": "<PERSON><PERSON><PERSON>", "product": "Produk", "material": "<PERSON><PERSON>"}, "showOnMenuOption": {"semua": "<PERSON><PERSON><PERSON>", "tampilMenu": "Tampil di Menu", "tidakTampil": "Tidak Tampil di Menu"}, "timeOption": {"today": "<PERSON>", "yesterday": "<PERSON><PERSON><PERSON>", "3day": "3 <PERSON> yang lalu", "7day": "7 <PERSON> yang lalu"}, "product": {"option": {"product": "Produk", "package": "<PERSON><PERSON>"}}}, "warning": {"capitalMoreThanPurchasePrice": "Terdapat beberapa data yang memiliki harga modal lebih besar daripada harga jual.", "blockDownloadLaporan": "Upgrade layanan anda sekarang menjadi <strong>Starter</strong>, <strong>Advance</strong> atau <strong>Prime</strong> untuk mendapatkan akses ekspor laporan.", "apkVersion": "{{ context }} hanya dapat digunakan pada aplikasi versi {{version}}."}, "suggest": {"continueAddMaterialOrCancel": "Silahkan pilih lanjutkan untuk menambahkan bahan, atau tekan Batal untuk membatalkan."}, "error": {"saveFailed": "Simpan data gagal", "changeFailed": "Gagal merubah data", "fileNotFound": "File tidak ditemukan di server", "exportFail": "Gagal mengekspor file", "importFail": "Impor Data Gagal", "uploadFail": "Upload gagal!", "unchoosedFile": "Mohon pilih file terlebih dahulu", "unmatchLengthRequirementsItem": "<PERSON><PERSON> {{notMatchReq}}", "unregisteredValueQty": "Jumlah {{entity}} {{notMatchReq}}", "fileUploadNotXLS": "File tidak didukung, pastikan file bertipe XLS / XLSX.", "unchoosedOutlet": "<PERSON><PERSON>", "unchoosedFileWarning": "Belum Pilih File untuk diunggah", "duplicateNames": "Duplik<PERSON> Nama", "duplicateSKUs": "Duplikasi SKU", "oneOfDimensionProductNotMatched": "Terdapat {{ skuLength }} satuan product yang berukuran tidak sesuai ketentuan.", "someSKUOrNameAreSame": "Terdapat beberapa row dengan kode SKU / nama yang sama (duplikasi).<br />Harap periksa kembali data anda, pastikan tidak ada spasi / karakter kosong dibelakang kode SKU atau nama produk.", "someSKUAreNotRegistered": "Terdapat beberapa kode SKU yang tidak terdaftar sebelumnya, mohon periksa kembali file anda.", "someSKUOrNameAreRegistered": "Terdapat beberapa row dengan kode SKU / nama yang telah terdaftar.", "loginSessionIsOver": "Sesi login telah be<PERSON>hir", "failedGetCategoryList": "Gagal mendapatkan list kategori", "failedGetData": "Gagal mengambil data", "failedGetDetailData": "Gagal mengambil data detail", "failedFetch": "Gagal mengambil data", "failedGetDataCustom": "Gagal mengambil data {{ data }}", "failedEditData": "Gagal mengubah data!", "failedGetTotalProduct": "Gagal mengambil Data Total Produk", "failedGetServiceHour": "Gagal mendapatkan Service Hour", "failedUploadImage": "Upload Image Gagal!", "smallImageResolution": "Resolusi image terlalu kecil (min: {{minWidth}}x{{minHeight}} px)", "largeImageResolution": "Resolusi image terlalu besar (max: {{minWidth}}x{{minHeight}} px)", "largeImageFile": "File image terlalu besar (max: 1 MB)", "smallImageFile": "File image terlalu kecil (min: 10 Kb)", "unsuportedImageFile": "Format file tidak didukung !", "supportedImageFile": "Pastikan file memiliki format yg didukung (JPG, JPEG, PNG)", "formatUnsupported": "Format berkas tidak didukung"}, "success": {"export": "<PERSON><PERSON><PERSON> <strong>{{ data }}</strong> ber<PERSON><PERSON> diekspor", "exportProcess": "<strong>{{ data }}</strong> dalam proses ekspor", "failedGetTotalProduct": "Gagal mengambil Data Total Produk", "failedGetServiceHour": "Gagal mendapatkan Service Hour"}, "toast": {"success": "Ber<PERSON>il!", "error": "Gagal!", "error2": "Gagal!", "disabled": "Dinonaktifkan!", "somethingWrong": "<PERSON><PERSON><PERSON><PERSON>", "unactivated": "Dinonaktifkan!", "failGotData": "Gagal mendapatkan data", "successSaveSetting": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON><PERSON> disimpan", "successSaveData": "Berhasil menyimpan data!", "successEditData": "Berhasil mengubah data!", "successDeleteData": "Be<PERSON><PERSON><PERSON> menghapus data!", "failedDeleteData": "Gagal menghapus data!", "successOrderSynchronization": "Sinkronisasi Pesanan sedang be<PERSON>, mohon tunggu beberapa saat hingga seluruh pesanan ber<PERSON>il tersingkronisasi", "successAddProduct": "<PERSON><PERSON><PERSON><PERSON> produk", "successEditProduct": "<PERSON><PERSON><PERSON><PERSON> produk", "successDeleteProduct": "<PERSON><PERSON><PERSON><PERSON> produk", "successAutoStock": "Auto stock berhasil diaktifkan", "errorAddProduct": "Gagal menambah produk", "errorEditProduct": "Gagal mengubah produk", "errorDeleteProduct": "<PERSON><PERSON> produk", "errorTemplate": "Template t<PERSON><PERSON>, gunakan Template yang disediakan", "online": {"title": "Online!", "description": "Anda kembali terhubung ke jaringan internet"}, "offline": {"title": "Internet Bermasalah", "description": "Tidak ada koneksi internet, mohon periksa jaringan"}, "successAddFavorite": "Menu <strong>{{menu}}</strong> berhasil ditambahkan ke menu favorit", "favoriteLimit": "<PERSON>a telah mencapai batas untuk jumlah <PERSON> (maks. 10).<br/>Hapus menu favorit lainnya dan silakan coba lagi", "successRemoveFavorite": "Menu <strong>{{menu}}</strong> berhasil dihapus dari menu favorit", "cancelled": "Di<PERSON><PERSON><PERSON>", "redirectToPrevPage": "Anda diara<PERSON>kan ke halaman sebelumnya", "failedToGetData": "Gagal mendapatkan data, coba beberapa saat lagi atau hubungi tim support kami.", "errorMaxDate": "<PERSON><PERSON><PERSON><PERSON>!", "errorMaxDateRange": "<PERSON><PERSON><PERSON><PERSON> rentang waktu yang dapat dipilih: 3 bulan", "errorMaxDateRangeYear": "<PERSON>ks<PERSON><PERSON> rentang waktu yang dapat dipilih: 1 tahun", "errorMax2MonthsDateRange": "<PERSON><PERSON><PERSON><PERSON> rentang waktu yang dapat dipilih: 2 bulan", "process": "Proses!"}, "days": {"monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON><PERSON>", "wednesday": "<PERSON><PERSON>", "thusday": "<PERSON><PERSON>", "friday": "<PERSON><PERSON>", "saturday": "Sabtu", "sunday": "<PERSON><PERSON>", "online": {"title": "Online!", "description": "Anda kembali terhubung ke jaringan internet"}, "offline": {"title": "Internet Bermasalah", "description": "Tidak ada koneksi internet, mohon periksa jaringan"}, "successAddProduct": "<PERSON><PERSON><PERSON><PERSON> produk", "successEditProduct": "<PERSON><PERSON><PERSON><PERSON> produk", "successDeleteProduct": "<PERSON><PERSON><PERSON><PERSON> produk", "errorAddProduct": "Gagal menambah produk", "errorEditProduct": "Gagal mengubah produk", "errorDeleteProduct": "<PERSON><PERSON> produk", "successEditData": "Berhasil mengubah data!", "successDeleteData": "Be<PERSON><PERSON><PERSON> menghapus data!", "successOrderSynchronization": "Sinkronisasi Pesanan sedang be<PERSON>, mohon tunggu beberapa saat hingga seluruh pesanan ber<PERSON>il tersingkronisasi"}, "favorite": {"title": "<PERSON><PERSON>", "favoriteEmpty": "Tambahkan menu yang sering diakses ke daftar Menu Favorit dengan klik ikon Bintang pada tiap halaman. Pilih maksimal 10 menu", "desktopOnly": "Hanya dapat dibuka di tampilan desktop"}, "subscription": {"unauthorizedBlocker": {"description": "<PERSON><PERSON>, fitur ini hanya bisa diakses oleh pengguna", "upgradeButton": "Upgrade Lang<PERSON><PERSON>", "and": "dan"}, "expiredSupport": {"description": "<PERSON>et layanan berl<PERSON>an {{accountType}} pada cabang {{outlets}} telah berakhir", "descriptionPopup": "<PERSON><PERSON> layanan be<PERSON> <strong>{{accountType}}</strong> pada cabang <strong>{{outletName}}</strong> telah berakhir", "updateButton": "Perpanjang", "desciptionDialog": "Masa aktif paket <b>{{accountType}}</b> pada outlet <b>{{outletName}}</b> telah be<PERSON> sejak <b>{{expiredDate}}</b>. <PERSON><PERSON>an perbarui paket berlangganan anda.", "thisOutlet": "outlet ini", "activePeriod": "Masa aktif akun {{accountType}} kamu", "expireInDays": "{{day}} <PERSON> lagi", "expireToday": "be<PERSON><PERSON> hari ini", "toastTitle": "Langganan Telah Berakhir!", "toastDesc": "Gagal mengambil data outlet. Untuk melanjutkan, aktifkan kembali langganan outlet utama Anda"}, "additionalSupport": {"description": "<PERSON><PERSON>n support tambahan <strong>{{supportName}}</strong> <PERSON><PERSON> te<PERSON> be<PERSON>", "noSupport": "<PERSON>a tidak memiliki layanan support tambahan <strong>{{supportName}}</strong>", "buyBackButton": "<PERSON><PERSON>", "buySupportButton": "<PERSON><PERSON> {{supportName}} Sekarang"}, "trial": {"description": "Masa Aktif akun {{accountType}} {{dayText}} Segera beli langganan sebelum masa trial berakhir untuk mendapatkan diskon berlangganan hingga 35%", "expireInDays": "tersisa {{day}} hari", "expireToday": "be<PERSON><PERSON> hari ini"}}, "chooseOutlet": {"title": "Ganti Outlet?", "confirm": "Ya, Ganti Outlet", "cancel": "<PERSON><PERSON>", "description": "Melanjutkan mengganti outlet akan mereset form yang telah diisi", "placeholder": "<PERSON><PERSON><PERSON>"}, "salesIncreasement": {"label": "<PERSON>gan pakai majoo, pen<PERSON><PERSON><PERSON> bulan ini meningkat senilai", "tooltip": "<PERSON>lai ini diambil dari total penjualan dengan promo bulan ini"}, "export": {"description": "<strong>{{reportName}}</strong> pada periode <strong>{{startDate}}</strong> sampai <strong>{{endDate}}</strong> akan diekspor dalam format <strong>{{type}}</strong> dan disimpan di perangkat yang digunakan. Lanjutkan?", "descriptionConfirmExport": "<PERSON><PERSON>, sistem sedang memproses <strong>{{menu}}</strong>. <PERSON>a akan menerima pemberitahuan notifikasi dan email saat data siap untuk diunduh."}, "csvDelimiter": {"semicolon": "Titik koma ( ; )", "tab": "Tab", "pipe": "Pipa ( | )"}, "evidenceUploader": {}}